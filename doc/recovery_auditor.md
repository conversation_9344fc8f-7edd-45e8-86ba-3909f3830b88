# Аудитор целостности состояния (run_00_recovery.py)

## Концепция

`run_00_recovery.py` — это **аудитор**, а не "ремонтник". Его задача — находить и устранять несоответствия между состоянием задач в Redis и состоянием книг в PostgreSQL/S3, которые возникают из-за сбоев воркеров.

## Философия

1. **Безопасный откат, а не доделывание**: Скрипт не пытается завершить работу упавшего воркера. Он чисто и безопасно откатывает состояние задачи/книги до точки, с которой система сможет подхватить ее заново.

2. **Идемпотентность**: Повторный запуск скрипта на уже исправленной системе не должен приводить ни к каким изменениям.

3. **Безопасность по умолчанию**: Основной режим работы — анализ (`--dry-run`). Любые изменяющие действия требуют явного указания флага (`--fix`).

4. **Четкое разделение**: Логика разделена на два независимых модуля, каждый из которых отвечает за свой, конкретный тип сбоя.

## Архитектура

### Главный координатор: SystemRecovery
Координирует работу двух независимых аудиторов и предоставляет единый интерфейс.

### Аудитор 1: StaleTaskAuditor
- **Фокус**: Redis
- **Проблема**: Воркер умер, не успев завершить задачу. Задача "зависла" в очереди `processing`, но ее блокировка в Redis истекла.
- **Решение**: Вернуть задачу в очередь `new` для повторной обработки.

### Аудитор 2: IncompleteBookAuditor  
- **Фокус**: PostgreSQL + Файловое хранилище (S3/локальное)
- **Проблема**: Воркер умер после сохранения метаданных в БД (`status=10`), но до создания файла-артефакта.
- **Решение**: Полностью удалить все следы этой незавершенной транзакции из системы, чтобы сканер мог найти эту книгу заново.

## Использование

### Основные команды

```bash
# Анализ без изменений (безопасный режим по умолчанию)
python run_00_recovery.py

# Исправление найденных проблем
python run_00_recovery.py --fix

# Аудит только зависших задач в Redis
python run_00_recovery.py --skip-books

# Аудит только незавершенных книг в PostgreSQL
python run_00_recovery.py --skip-tasks

# Справка
python run_00_recovery.py --help
```

### Примеры вывода

#### Система в порядке
```
🎉 Система в порядке! Проблем не обнаружено.
```

#### Найдены проблемы (dry-run)
```
📈 ИТОГОВАЯ СТАТИСТИКА:
   🔍 Всего проблем найдено:        5
   📋 Планируется исправить: 5
   ❌ Всего ошибок:                 0

⚠️  Для исправления проблем запустите скрипт с флагом --fix
```

#### Проблемы исправлены
```
📈 ИТОГОВАЯ СТАТИСТИКА:
   🔍 Всего проблем найдено:        5
   🔧 Исправлено: 5
   ❌ Всего ошибок:                 0

✅ Восстановление завершено успешно!
```

## Алгоритмы работы

### StaleTaskAuditor

1. **Поиск зависших задач**:
   - Получает все задачи из `QUEUE_PARSING_PROCESSING`
   - Проверяет поле `_claimed_at` каждой задачи
   - Если `current_time - _claimed_at > WORKER_TIMEOUT`, задача считается зависшей

2. **Восстановление**:
   - Проверяет наличие блокировки `lock:book:{source_type}:{source_id}`
   - Если блокировка существует → пропускает (воркер еще жив)
   - Если блокировки нет → атомарно перемещает задачу из `processing` в `new`

### IncompleteBookAuditor

1. **Поиск незавершенных книг**:
   - Выполняет SQL: `SELECT * FROM books WHERE process_status = 10`
   - Получает список книг с сохраненными метаданными, но без артефактов

2. **Восстановление**:
   - Проверяет существование артефакта через `_get_artifact_path(book_id)`
   - **Если артефакт существует** → обновляет статус на 20 (редкий случай)
   - **Если артефакта нет** → полный откат:
     - Удаляет книгу из PostgreSQL (CASCADE удалит связанные записи)
     - Удаляет ключ из `SET_QUEUED_IDS` в Redis

## Безопасность и надежность

### Атомарность операций
- Все операции с Redis выполняются через `pipeline()`
- Операции с PostgreSQL выполняются в транзакциях
- При ошибках выполняется `rollback`

### Обработка ошибок
- Каждый аудитор ведет детальную статистику ошибок
- Ошибки логируются с полным стек-трейсом
- Сбой одного аудитора не влияет на работу другого

### Идемпотентность
- Повторный запуск на исправленной системе не вносит изменений
- Все проверки выполняются заново при каждом запуске

## Мониторинг и логирование

### Уровни логирования
- `INFO`: Основные этапы работы и результаты
- `DEBUG`: Детальная информация о каждой задаче/книге
- `WARNING`: Некритичные проблемы (поврежденные задачи, отсутствующие поля)
- `ERROR`: Критичные ошибки с полным стек-трейсом

### Метрики
- Количество найденных проблем по типам
- Количество исправленных проблем
- Количество ошибок при обработке
- Время выполнения

## Тестирование

Полный набор интеграционных тестов находится в `tools/test_recovery.py`:

```bash
# Запуск всех тестов
python tools/test_recovery.py

# Установка зависимостей для тестов
pip install fakeredis
```

Тесты покрывают все сценарии:
- ✅ Поиск и восстановление зависших задач
- ✅ Поиск и откат незавершенных книг  
- ✅ Режимы dry-run и fix
- ✅ Обработка заблокированных задач
- ✅ Обработка ошибок

## Интеграция в мониторинг

Рекомендуется запускать аудитор:
- **Ежечасно** в режиме `--dry-run` для мониторинга
- **По требованию** в режиме `--fix` для исправления проблем
- **После сбоев** воркеров для очистки состояния

### Пример cron задачи
```bash
# Ежечасный мониторинг
0 * * * * cd /path/to/books && python run_00_recovery.py >> /var/log/recovery_audit.log 2>&1

# Еженедельная очистка (осторожно!)
0 2 * * 0 cd /path/to/books && python run_00_recovery.py --fix >> /var/log/recovery_fix.log 2>&1
```

## Связь с другими компонентами

- **scanner_inventorizer.py**: Создает задачи, которые аудитор может восстанавливать
- **run_20_process_book_worker.py**: Создает блокировки, которые аудитор проверяет
- **BookProcessor**: Создает записи со статусом 10, которые аудитор может откатывать
- **artifact_saver.py**: Создает артефакты, наличие которых проверяет аудитор
